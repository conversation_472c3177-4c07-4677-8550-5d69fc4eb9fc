import { SVGProps } from "react"

const DocumentIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="13"
    height="12"
    viewBox="0 0 13 12"
    fill="none"
    {...props}
  >
    <g clipPath="url(#clip0_21144_3581)">
      <path
        d="M8.48145 0H3.98145C2.46445 0 1.23145 1.233 1.23145 2.75V9.25C1.23145 10.767 2.46445 12 3.98145 12H8.48145C9.99845 12 11.2314 10.767 11.2314 9.25V2.75C11.2314 1.233 9.99845 0 8.48145 0ZM6.23145 7H4.48145C4.06745 7 3.73145 6.664 3.73145 6.25C3.73145 5.836 4.06745 5.5 4.48145 5.5H6.23145C6.64545 5.5 6.98145 5.836 6.98145 6.25C6.98145 6.664 6.64545 7 6.23145 7ZM7.98145 4.5H4.48145C4.06745 4.5 3.73145 4.164 3.73145 3.75C3.73145 3.336 4.06745 3 4.48145 3H7.98145C8.39545 3 8.73145 3.336 8.73145 3.75C8.73145 4.164 8.39545 4.5 7.98145 4.5Z"
        fill="#18181B"
      />
    </g>
    <defs>
      <clipPath id="clip0_21144_3581">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="translate(0.231445)"
        />
      </clipPath>
    </defs>
  </svg>
)

export default DocumentIcon

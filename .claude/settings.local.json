{"permissions": {"allow": ["Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(ls:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(find:*)", "Bash(npm run seed:*)", "Bash(npm install)", "Bash(npx tsc:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./dev-setup.sh:*)", "<PERSON><PERSON>(docker-compose down:*)", "Bash(docker-compose build:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(yarn --version)", "Bash(yarn dev)", "Bash(corepack enable)", "Bash(DATABASE_URL=postgres://medusa_user:medusa_password@localhost:5432/medusa_db REDIS_URL=redis://localhost:6379 JWT_SECRET=test-secret COOKIE_SECRET=test-secret yarn dev)", "Bash(env DATABASE_URL=postgres://medusa_user:medusa_password@localhost:5432/medusa_db REDIS_URL=redis://localhost:6379 JWT_SECRET=test-secret COOKIE_SECRET=test-secret yarn dev)", "Bash(bash:*)", "Bash(yarn install)", "<PERSON><PERSON>(curl:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npx medusa db:setup:*)", "Bash(npx medusa:*)", "Bash(yarn install:*)", "Bash(yarn list:*)", "Bash(yarn why:*)", "Bash(node:*)", "<PERSON><PERSON>(touch:*)", "Bash(npm ls:*)", "Bash(yarn medusa:*)", "<PERSON><PERSON>(timeout 30s yarn dev)", "<PERSON><PERSON>(docker compose:*)", "Bash(DATABASE_URL=postgres://medusa_user:medusa_password@localhost:5432/medusa_db REDIS_URL=redis://localhost:6379 yarn dev)", "Bash(yarn add:*)"], "deny": []}}
# Development overrides for docker-compose.yml
# This file is automatically loaded by docker-compose in development

services:
  backend:
    environment:
      # Enable hot reloading
      CHOKIDAR_USEPOLLING: "true"
      WATCHPACK_POLLING: "true"
    volumes:
      # Bind mount for hot reloading
      - ./backend:/app:cached
      - backend_node_modules:/app/node_modules
      # Exclude specific directories from sync for performance
      - /app/.medusa
      - /app/dist
    # Development specific ports
    ports:
      - "9000:9000"  # API
      - "7001:7001"  # Admin
      - "9229:9229"  # Debug port
    # Override command to install dependencies first in dev mode
    command: sh -c "yarn install && yarn dev"

  storefront:
    environment:
      # Enable hot reloading
      CHOKIDAR_USEPOLLING: "true"
      WATCHPACK_POLLING: "true"
    volumes:
      # Bind mount for hot reloading
      - ./storefront:/app:cached
      - storefront_node_modules:/app/node_modules
      # Exclude .next for performance
      - /app/.next
    # Development specific ports
    ports:
      - "8000:8000"  # Next.js dev server
      - "9230:9230"  # Debug port
    # Override command to install dependencies first in dev mode
    command: sh -c "yarn install && yarn dev"

  # Optional: Database admin tool
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: medusa-pgadmin
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - medusa-network
    profiles:
      - admin-tools

  # Optional: Redis admin tool
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: medusa-redis-commander
    restart: unless-stopped
    depends_on:
      - redis
    environment:
      REDIS_HOSTS: "local:redis:6379"
      HTTP_USER: admin
      HTTP_PASSWORD: admin
    ports:
      - "8081:8081"
    networks:
      - medusa-network
    profiles:
      - admin-tools

volumes:
  pgadmin_data:
    driver: local
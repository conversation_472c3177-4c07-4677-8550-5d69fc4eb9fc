services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: medusa-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: medusa_db
      POSTGRES_USER: medusa_user
      POSTGRES_PASSWORD: medusa_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - medusa-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U medusa_user -d medusa_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: medusa-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - medusa-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Medusa Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: medusa-backend
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      NODE_ENV: development
      DATABASE_URL: ****************************************************/medusa_db
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-jwt-secret-change-in-production
      COOKIE_SECRET: your-cookie-secret-change-in-production
      MEDUSA_ADMIN_ONBOARDING_TYPE: default
      STORE_CORS: http://localhost:8000,http://localhost:3000
      ADMIN_CORS: http://localhost:7001,http://localhost:9000
      AUTH_CORS: http://localhost:8000,http://localhost:3000,http://localhost:7001,http://localhost:9000
    ports:
      - "9000:9000"
      - "7001:7001"
    volumes:
      - ./backend:/app
      - backend_node_modules:/app/node_modules
      - ./backend/static:/app/static
    networks:
      - medusa-network

  # Next.js Storefront
  storefront:
    build:
      context: ./storefront
      dockerfile: Dockerfile.dev
    container_name: medusa-storefront
    restart: unless-stopped
    depends_on:
      - backend
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_MEDUSA_BACKEND_URL: http://localhost:9000
      REVALIDATE_SECRET: your-revalidate-secret
    ports:
      - "8000:8000"
    volumes:
      - ./storefront:/app
      - storefront_node_modules:/app/node_modules
      - /app/.next
    networks:
      - medusa-network
    command: yarn dev

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_node_modules:
    driver: local
  storefront_node_modules:
    driver: local

networks:
  medusa-network:
    driver: bridge
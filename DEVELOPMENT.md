# Development Environment Setup

This project uses <PERSON>er Compose to set up a complete development environment for the Medusa B2B starter.

## Quick Start

1. **Run the setup script:**
   ```bash
   ./dev-setup.sh
   ```

2. **Access the applications:**
   - 🚀 **Medusa Admin**: http://localhost:7001
   - 🏪 **Storefront**: http://localhost:8000
   - 📡 **Backend API**: http://localhost:9000
   - 🗄️ **PostgreSQL**: localhost:5432
   - ⚡ **Redis**: localhost:6379

## Services

### Core Services
- **postgres**: PostgreSQL 16 database
- **redis**: Redis 7 for caching and sessions
- **backend**: Medusa backend (Node.js)
- **storefront**: Next.js storefront

### Optional Admin Tools
Run with admin tools:
```bash
docker-compose --profile admin-tools up -d
```

- **pgAdmin**: Database admin at http://localhost:5050
  - Email: `<EMAIL>`
  - Password: `admin`
- **Redis Commander**: Redis admin at http://localhost:8081
  - Username: `admin`
  - Password: `admin`

## Environment Configuration

### Backend (.env)
```bash
DATABASE_URL=postgres://medusa_user:medusa_password@localhost:5432/medusa_db
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-jwt-secret-change-in-production
COOKIE_SECRET=your-cookie-secret-change-in-production
```

### Storefront (.env.local)
```bash
NEXT_PUBLIC_MEDUSA_BACKEND_URL=http://localhost:9000
REVALIDATE_SECRET=your-revalidate-secret
```

## Common Commands

### Development
```bash
# Start all services
docker-compose up -d

# Start with admin tools
docker-compose --profile admin-tools up -d

# View logs
docker-compose logs -f [service_name]

# Restart a service
docker-compose restart [service_name]

# Execute commands in containers
docker-compose exec backend yarn seed
docker-compose exec storefront yarn build

# Access shell
docker-compose exec backend sh
docker-compose exec storefront sh
```

### Database Management
```bash
# Run migrations
docker-compose exec backend yarn medusa migrations run

# Seed database
docker-compose exec backend yarn seed

# Access PostgreSQL
docker-compose exec postgres psql -U medusa_user -d medusa_db
```

### Cleanup
```bash
# Stop all services
docker-compose down

# Stop and remove volumes (⚠️ This will delete your data)
docker-compose down -v

# Remove unused images
docker system prune -f
```

## Development Features

### Hot Reloading
- Both backend and storefront support hot reloading
- File changes are automatically detected and trigger rebuilds
- Node modules are cached in Docker volumes for performance

### Debug Support
- Backend debug port: 9229
- Storefront debug port: 9230
- Configure your IDE to attach to these ports

### Volume Management
- Source code is bind-mounted for development
- Node modules are stored in Docker volumes for performance
- Database and Redis data persist between container restarts

## Troubleshooting

### Services won't start
1. Check Docker daemon is running: `docker info`
2. Check port conflicts: `docker-compose ps`
3. View service logs: `docker-compose logs [service_name]`

### Database connection issues
1. Wait for database health check: `docker-compose logs postgres`
2. Verify connection: `docker-compose exec postgres pg_isready -U medusa_user`
3. Check environment variables in `.env` files

### Performance issues
1. Check available resources: `docker stats`
2. Prune unused containers: `docker system prune -f`
3. Restart Docker daemon if needed

### Port conflicts
Default ports used:
- 5432: PostgreSQL
- 6379: Redis
- 7001: Medusa Admin
- 8000: Storefront
- 9000: Backend API
- 5050: pgAdmin (optional)
- 8081: Redis Commander (optional)

Change ports in `docker-compose.override.yml` if needed.

## Best Practices

### Security
- Change default passwords in production
- Use environment-specific secrets
- Don't commit `.env` files to version control

### Performance
- Use Docker volumes for node_modules
- Enable BuildKit for faster builds: `export DOCKER_BUILDKIT=1`
- Regularly clean up unused images and containers

### Development Workflow
1. Make code changes in your editor
2. Changes are automatically reflected in running containers
3. Use `docker-compose logs -f` to monitor for errors
4. Run tests with `docker-compose exec [service] yarn test`

## Production Deployment

This setup is optimized for development. For production:
1. Use production Dockerfiles (without dev dependencies)
2. Use managed databases instead of containers
3. Implement proper secrets management
4. Add reverse proxy (nginx/traefik)
5. Configure monitoring and logging
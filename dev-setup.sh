#!/bin/bash

# Development Setup Script for Medusa B2B Starter
# This script sets up the development environment with best practices

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command_exists docker; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "All prerequisites are met"
}

# Create environment files
create_env_files() {
    log_info "Creating environment files..."
    
    # Backend .env
    if [ ! -f "./backend/.env" ]; then
        cat > ./backend/.env << 'EOF'
# Database
DATABASE_URL=postgres://medusa_user:medusa_password@localhost:5432/medusa_db

# Redis
REDIS_URL=redis://localhost:6379

# JWT & Cookie secrets (change these in production)
JWT_SECRET=your-jwt-secret-change-in-production
COOKIE_SECRET=your-cookie-secret-change-in-production

# Admin settings
MEDUSA_ADMIN_ONBOARDING_TYPE=default

# CORS settings
STORE_CORS=http://localhost:8000,http://localhost:3000
ADMIN_CORS=http://localhost:7001,http://localhost:9000

# File storage
FILE_SERVICE=local
STATIC_PATH=./static

# Development settings
NODE_ENV=development
EOF
        log_success "Created backend/.env"
    else
        log_warning "backend/.env already exists, skipping..."
    fi
    
    # Storefront .env.local
    if [ ! -f "./storefront/.env.local" ]; then
        cat > ./storefront/.env.local << 'EOF'
# Medusa Backend URL
NEXT_PUBLIC_MEDUSA_BACKEND_URL=http://localhost:9000

# Revalidation secret
REVALIDATE_SECRET=your-revalidate-secret

# Development settings
NODE_ENV=development
EOF
        log_success "Created storefront/.env.local"
    else
        log_warning "storefront/.env.local already exists, skipping..."
    fi
}

# Create database initialization script
create_init_scripts() {
    log_info "Creating database initialization scripts..."
    
    mkdir -p ./init-scripts
    
    if [ ! -f "./init-scripts/01-init.sql" ]; then
        cat > ./init-scripts/01-init.sql << 'EOF'
-- Create additional databases if needed
-- CREATE DATABASE medusa_test;
-- GRANT ALL PRIVILEGES ON DATABASE medusa_test TO medusa_user;
EOF
        log_success "Created database initialization script"
    fi
}

# Clean up existing containers and volumes
cleanup() {
    log_info "Cleaning up existing containers and volumes..."
    
    # Stop and remove containers
    docker-compose down --remove-orphans 2>/dev/null || true
    
    # Remove volumes (optional - comment out if you want to preserve data)
    # docker-compose down -v 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# Build and start services
start_services() {
    log_info "Building and starting services..."
    
    # Build images
    log_info "Building Docker images..."
    docker-compose build --no-cache
    
    # Start services
    log_info "Starting services..."
    docker-compose up -d postgres redis
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose exec -T postgres pg_isready -U medusa_user -d medusa_db >/dev/null 2>&1; then
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "Database failed to start within 60 seconds"
        exit 1
    fi
    
    log_success "Database is ready"
    
    # Start backend
    log_info "Starting backend service..."
    docker-compose up -d backend
    
    # Wait for backend to be ready
    log_info "Waiting for backend to be ready..."
    timeout=120
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:9000/health >/dev/null 2>&1; then
            break
        fi
        sleep 5
        timeout=$((timeout - 5))
    done
    
    if [ $timeout -le 0 ]; then
        log_warning "Backend health check failed, but continuing..."
    else
        log_success "Backend is ready"
    fi
    
    # Start storefront
    log_info "Starting storefront service..."
    docker-compose up -d storefront
    
    log_success "All services started successfully"
}

# Run database migrations and seeding
setup_database() {
    log_info "Setting up database..."
    
    # Wait a bit more for backend to be fully ready
    sleep 10
    
    # Run migrations (if needed)
    log_info "Running database setup..."
    docker-compose exec backend yarn seed || log_warning "Database seeding failed - this might be normal on first run"
    
    log_success "Database setup completed"
}

# Show service status and URLs
show_status() {
    log_info "Checking service status..."
    
    echo ""
    echo "=== SERVICE STATUS ==="
    docker-compose ps
    
    echo ""
    echo "=== APPLICATION URLS ==="
    echo -e "${GREEN}🚀 Medusa Admin:${NC} http://localhost:7001"
    echo -e "${GREEN}🏪 Storefront:${NC} http://localhost:8000"
    echo -e "${GREEN}📡 Backend API:${NC} http://localhost:9000"
    echo -e "${GREEN}🗄️  PostgreSQL:${NC} localhost:5432"
    echo -e "${GREEN}⚡ Redis:${NC} localhost:6379"
    
    echo ""
    echo "=== USEFUL COMMANDS ==="
    echo "View logs: docker-compose logs -f [service_name]"
    echo "Stop services: docker-compose down"
    echo "Restart service: docker-compose restart [service_name]"
    echo "Execute command: docker-compose exec [service_name] [command]"
    echo "Shell access: docker-compose exec [service_name] sh"
    
    echo ""
    echo -e "${GREEN}✅ Development environment is ready!${NC}"
}

# Main execution
main() {
    echo "=== Medusa B2B Development Setup ==="
    echo ""
    
    check_prerequisites
    create_env_files
    create_init_scripts
    
    # Ask user if they want to clean up
    read -p "Do you want to clean up existing containers? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup
    fi
    
    start_services
    setup_database
    show_status
}

# Handle script termination
trap 'log_error "Script interrupted"; exit 1' INT TERM

# Run main function
main "$@"
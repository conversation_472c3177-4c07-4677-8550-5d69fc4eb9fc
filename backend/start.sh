#!/bin/sh

# Wait for dependencies to be ready
echo "Waiting for database to be ready..."
sleep 10

# Try to install dependencies, continue if it fails  
echo "Installing/updating dependencies..."
yarn install --network-timeout 100000 || echo "Dependency install failed, continuing with existing dependencies..."

# Run migrations and start server
echo "Running database migrations..."
npx medusa db:migrate

echo "Seeding database..."
yarn seed || echo "Seeding failed, continuing..."

echo "Starting Medusa development server..."
exec npx medusa develop --host 0.0.0.0 --port 9000
const { MedusaApp, loadEnv } = require("@medusajs/framework");
const { execSync } = require("child_process");

// Load environment variables
loadEnv(process.env.NODE_ENV || "development", process.cwd());

async function setupDatabase() {
  try {
    console.log("🚀 Starting database setup...");
    
    // Check if we can connect to the database
    console.log("Database URL:", process.env.DATABASE_URL ? "✅ Set" : "❌ Missing");
    console.log("Redis URL:", process.env.REDIS_URL ? "✅ Set" : "❌ Missing");
    
    if (!process.env.DATABASE_URL) {
      throw new Error("DATABASE_URL is not set");
    }

    // Initialize Medusa app to get access to the database
    console.log("📊 Initializing Medusa app...");
    const { shutdown } = await MedusaApp({
      database: {
        clientUrl: process.env.DATABASE_URL,
      },
    });

    console.log("✅ Database connection established successfully!");
    console.log("🎉 Database setup completed!");
    
    // Gracefully shutdown
    await shutdown();
    
  } catch (error) {
    console.error("❌ Database setup failed:", error.message);
    process.exit(1);
  }
}

setupDatabase();
{"name": "medusa-b2b-starter", "version": "0.0.1", "description": "A starter for Medusa B2B projects.", "author": "Medusa (https://medusajs.com)", "license": "MIT", "keywords": ["sqlite", "postgres", "typescript", "ecommerce", "headless", "medusa"], "scripts": {"build": "medusa build", "seed": "medusa exec ./src/scripts/seed.ts", "start": "medusa start", "dev": "medusa develop", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit"}, "dependencies": {"@medusajs/admin-sdk": "2.8.4", "@medusajs/cli": "2.8.4", "@medusajs/framework": "2.8.4", "@medusajs/js-sdk": "2.8.4", "@medusajs/medusa": "2.8.4", "@medusajs/ui": "4.0.14", "@mikro-orm/core": "6.4.3", "@mikro-orm/knex": "6.4.3", "@mikro-orm/migrations": "6.4.3", "@mikro-orm/postgresql": "6.4.3", "awilix": "^8.0.1", "pg": "^8.13.0", "posthog-node": "^4.17.1", "tailwindcss": "^4.1.12", "zod": "^3.25.0"}, "devDependencies": {"@medusajs/test-utils": "2.8.4", "@medusajs/ui-preset": "2.8.4", "@mikro-orm/cli": "6.4.3", "@swc/core": "^1.13.3", "@swc/jest": "^0.2.36", "@types/jest": "^29.5.14", "@types/node": "^20.0.0", "@types/react": "^18.3.2", "@types/react-dom": "^18.2.25", "jest": "^29.7.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "ts-node": "^10.9.2", "typescript": "^5.5.3", "vite": "^5.2.11"}, "engines": {"node": ">=20"}, "packageManager": "yarn@4.4.0", "resolutions": {"zod": "^3.25.0", "@swc/core": "^1.13.3"}}
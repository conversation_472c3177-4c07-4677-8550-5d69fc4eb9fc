FROM node:20-slim

# Install required system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    make \
    g++ \
    libcairo2-dev \
    libjpeg-dev \
    libpango1.0-dev \
    libgif-dev \
    build-essential \
    libvips-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Enable corepack for yarn
RUN corepack enable

WORKDIR /app

# Copy package files first for better layer caching
COPY package.json ./
COPY yarn.lock ./
COPY .yarnrc.yml ./

# Install dependencies
RUN yarn install --immutable --network-timeout 100000

# Copy source code
COPY . .

# Create uploads directory
RUN mkdir -p static/uploads

# Expose ports
EXPOSE 9000 7001

# Copy and set permissions for start script
COPY start.sh ./
RUN chmod +x start.sh

# Development command
CMD ["./start.sh"]
const { loadEnv } = require("@medusajs/framework/utils");
const express = require("express");

// Load environment variables
loadEnv(process.env.NODE_ENV || "development", process.cwd());

async function startServer() {
  try {
    console.log("🚀 Starting Medusa server...");
    console.log("Environment:", process.env.NODE_ENV || "development");
    console.log("Database URL:", process.env.DATABASE_URL ? "✅ Set" : "❌ Missing");
    console.log("Redis URL:", process.env.REDIS_URL ? "✅ Set" : "❌ Missing");
    
    // Import and start Medusa
    const { medusa } = require("@medusajs/framework");
    
    const app = await medusa({
      projectConfig: {
        databaseUrl: process.env.DATABASE_URL,
        http: {
          storeCors: process.env.STORE_CORS,
          adminCors: process.env.ADMIN_CORS,
          authCors: process.env.AUTH_CORS,
          jwtSecret: process.env.JWT_SECRET || "supersecret",
          cookieSecret: process.env.COOKIE_SECRET || "supersecret",
        },
      },
    });

    const port = process.env.PORT || 9000;
    const host = process.env.HOST || "localhost";

    app.listen(port, host, () => {
      console.log(`🎉 Medusa server is running at http://${host}:${port}`);
      console.log(`📊 Admin dashboard: http://${host}:${port}/app`);
    });

  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
}

startServer();